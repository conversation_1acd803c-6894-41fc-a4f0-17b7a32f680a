<?php

namespace App\Http\Controllers\Transact;

use App\Http\Controllers\Controller;
use App\Models\Transaction\MonitorDocContainer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ImportInvContainerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $invId = $request->input('InvId');
        $query = MonitorDocContainer::query();
        if ($invId) {
            $query->where('DocumentId', $invId);
        }
        $containers = $query->get();
        return response()->json(['rows' => $containers]);
    }

    /**
     * Store a newly created resource in storage (bulk insert/update).
     */
    public function store(Request $request)
    {
        $containers = $request->containers;
        if (!is_array($containers) || empty($containers)) {
            return response()->json(['errors' => true, 'message' => 'No containers provided'], 422);
        }

        $insert = 0;
        $update = 0;
        $errors = [];

        foreach ($containers as $index => $item) {
            $validator = Validator::make($item, [
                'InvId' => 'required|integer',
                'ContainerNo' => 'required|string|max:200',
                'ContainerSize' => 'required|string|max:100',
                'Remark' => 'nullable|string|max:200',
            ]);

            if ($validator->fails()) {
                $errors[$index] = $validator->errors();
                continue;
            }

            if (!empty($item['DocEntry'])) {
                // Update
                $container = MonitorDocContainer::find($item['DocEntry']);
                if ($container) {
                    $container->InvId = $item['InvId'];
                    $container->ContainerNo = $item['ContainerNo'];
                    $container->ContainerSize = $item['ContainerSize'];
                    $container->Remark = $item['Remark'] ?? null;
                    $container->save();
                    $update++;
                } else {
                    $errors[$index] = ['DocEntry' => ['Not found for update']];
                }
            } else {
                // Insert
                MonitorDocContainer::create([
                    'InvId' => $item['InvId'],
                    'ContainerNo' => $item['ContainerNo'],
                    'ContainerSize' => $item['ContainerSize'],
                    'Remark' => $item['Remark'] ?? null,
                    'CreatedBy' => Auth::id() ?? 0,
                ]);
                $insert++;
            }
        }

        return response()->json([
            'inserted' => $insert,
            'updated' => $update,
            'errors' => $errors,
            'message' => "Insert => $insert, Update => $update"
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $container = MonitorDocContainer::find($id);
        if (!$container) {
            return response()->json(['errors' => true, 'message' => 'Not found'], 404);
        }
        $container->delete();
        return response()->json(['success' => true]);
    }
}
